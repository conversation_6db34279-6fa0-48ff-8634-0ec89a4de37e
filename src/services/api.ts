import type { Service, ApiError } from '../types'

const apiBaseUrl = import.meta.env.VITE_API_BASE_URL

if (!apiBaseUrl) {
  throw new Error('Missing VITE_API_BASE_URL environment variable')
}

// Ensure the URL doesn't have a trailing slash
const API_BASE_URL = apiBaseUrl.replace(/\/$/, '')

class ApiService {
  private accessToken: string | null = null

  setAccessToken(token: string | null) {
    this.accessToken = token
  }

  private async getAuthHeaders(): Promise<Record<string, string>> {
    if (!this.accessToken) {
      throw new Error('No authentication token available')
    }

    return {
      'Authorization': `Bearer ${this.accessToken}`,
      'Content-Type': 'application/json',
    }
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const error: ApiError = {
        message: `HTTP ${response.status}: ${response.statusText}`,
        status: response.status,
      }
      
      try {
        const errorData = await response.json()
        error.message = errorData.message || error.message
      } catch {
        // Use default error message if JSON parsing fails
      }
      
      throw error
    }

    return response.json()
  }

  async getServices(): Promise<Service[]> {
    const headers = await this.getAuthHeaders()
    
    const response = await fetch(`${API_BASE_URL}/v1/services`, {
      method: 'GET',
      headers,
    })

    return this.handleResponse<Service[]>(response)
  }

  async getService(id: string): Promise<Service> {
    const headers = await this.getAuthHeaders()
    
    const response = await fetch(`${API_BASE_URL}/v1/services/${id}`, {
      method: 'GET',
      headers,
    })

    return this.handleResponse<Service>(response)
  }
}

export const apiService = new ApiService()
